{"version": "44.0.0", "files": {"2d51b762a8d3286a7a1dcf0730b35318c6cb2ada3b807003f3f961699b261f4e": {"displayName": "AwsmcpdemoStack Template", "source": {"path": "AwsmcpdemoStack.template.json", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "2d51b762a8d3286a7a1dcf0730b35318c6cb2ada3b807003f3f961699b261f4e.json", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}}, "dockerImages": {}}