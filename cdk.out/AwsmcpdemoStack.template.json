{"Resources": {"CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/zPSMzIw0DNUTCwv1k1OydbNyUzSCy5JTM7WyctPSdXLKtYvMzLSMzTWM1DMKs7M1C0qzSvJzE3VC4LQAEQkqP8/AAAA"}, "Metadata": {"aws:cdk:path": "AwsmcpdemoStack/CDKMetadata/Default"}}}, "Parameters": {"BootstrapVersion": {"Type": "AWS::SSM::Parameter::Value<String>", "Default": "/cdk-bootstrap/hnb659fds/version", "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]"}}, "Rules": {"CheckBootstrapVersion": {"Assertions": [{"Assert": {"Fn::Not": [{"Fn::Contains": [["1", "2", "3", "4", "5"], {"Ref": "BootstrapVersion"}]}]}, "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI."}]}}}