{"Description": "Import existing CloudFront distributions for CDK management", "Outputs": {"DistributionDomainName0": {"Description": "Domain name for Imported distribution E1A4N8KHARPVB2", "Value": "d6g1hzg988ekh.cloudfront.net"}, "DistributionId0": {"Description": "Distribution ID for Imported distribution E1A4N8KHARPVB2", "Value": "E1A4N8KHARPVB2"}, "DistributionArn0": {"Description": "ARN for Imported distribution E1A4N8KHARPVB2", "Value": "arn:aws:cloudfront::954976309477:distribution/E1A4N8KHARPVB2"}, "DistributionDomainName1": {"Description": "Domain name for Imported distribution E2CKW8QIG4YG9K", "Value": "d1ia0hyamdrxlh.cloudfront.net"}, "DistributionId1": {"Description": "Distribution ID for Imported distribution E2CKW8QIG4YG9K", "Value": "E2CKW8QIG4YG9K"}, "DistributionArn1": {"Description": "ARN for Imported distribution E2CKW8QIG4YG9K", "Value": "arn:aws:cloudfront::954976309477:distribution/E2CKW8QIG4YG9K"}, "DistributionDomainName2": {"Description": "Domain name for Imported distribution E31WE94EAQ0907", "Value": "d2ygag17vkxn4c.cloudfront.net"}, "DistributionId2": {"Description": "Distribution ID for Imported distribution E31WE94EAQ0907", "Value": "E31WE94EAQ0907"}, "DistributionArn2": {"Description": "ARN for Imported distribution E31WE94EAQ0907", "Value": "arn:aws:cloudfront::954976309477:distribution/E31WE94EAQ0907"}, "TotalDistributions": {"Description": "Total number of imported CloudFront distributions", "Value": "3"}}, "Resources": {"CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/zPSMzIw0DNUTCwv1k1OydbNyUzSqw4uSUzO1glKLc4vLUpO1XFOy/MvLSkoLanVyctPSdXLKtYvMzLSMzTWM1DMKs7M1C0qzSvJzE3VC4LQAGr4TE5UAAAA"}, "Metadata": {"aws:cdk:path": "CloudFrontImportStack/CDKMetadata/Default"}}}, "Parameters": {"BootstrapVersion": {"Type": "AWS::SSM::Parameter::Value<String>", "Default": "/cdk-bootstrap/hnb659fds/version", "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]"}}, "Rules": {"CheckBootstrapVersion": {"Assertions": [{"Assert": {"Fn::Not": [{"Fn::Contains": [["1", "2", "3", "4", "5"], {"Ref": "BootstrapVersion"}]}]}, "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI."}]}}}