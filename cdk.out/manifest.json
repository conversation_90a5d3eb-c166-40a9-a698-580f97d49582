{"version": "44.0.0", "artifacts": {"AwsmcpdemoStack.assets": {"type": "cdk:asset-manifest", "properties": {"file": "AwsmcpdemoStack.assets.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "AwsmcpdemoStack": {"type": "aws:cloudformation:stack", "environment": "aws://unknown-account/unknown-region", "properties": {"templateFile": "AwsmcpdemoStack.template.json", "terminationProtection": false, "validateOnSynth": false, "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-deploy-role-${AWS::AccountId}-${AWS::Region}", "cloudFormationExecutionRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-cfn-exec-role-${AWS::AccountId}-${AWS::Region}", "stackTemplateAssetObjectUrl": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/2d51b762a8d3286a7a1dcf0730b35318c6cb2ada3b807003f3f961699b261f4e.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version", "additionalDependencies": ["AwsmcpdemoStack.assets"], "lookupRole": {"arn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-lookup-role-${AWS::AccountId}-${AWS::Region}", "requiresBootstrapStackVersion": 8, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "dependencies": ["AwsmcpdemoStack.assets"], "metadata": {"/AwsmcpdemoStack/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/AwsmcpdemoStack/CDKMetadata/Condition": [{"type": "aws:cdk:logicalId", "data": "CDKMetadataAvailable"}], "/AwsmcpdemoStack/BootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "BootstrapVersion"}], "/AwsmcpdemoStack/CheckBootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "CheckBootstrapVersion"}]}, "displayName": "AwsmcpdemoStack"}, "Tree": {"type": "cdk:tree", "properties": {"file": "tree.json"}}}, "minimumCliVersion": "2.1018.1"}