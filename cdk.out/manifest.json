{"version": "44.0.0", "artifacts": {"AwsmcpdemoStack.assets": {"type": "cdk:asset-manifest", "properties": {"file": "AwsmcpdemoStack.assets.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "AwsmcpdemoStack": {"type": "aws:cloudformation:stack", "environment": "aws://954976309477/us-east-1", "properties": {"templateFile": "AwsmcpdemoStack.template.json", "terminationProtection": false, "validateOnSynth": false, "assumeRoleArn": "arn:${AWS::Partition}:iam::954976309477:role/cdk-hnb659fds-deploy-role-954976309477-us-east-1", "cloudFormationExecutionRoleArn": "arn:${AWS::Partition}:iam::954976309477:role/cdk-hnb659fds-cfn-exec-role-954976309477-us-east-1", "stackTemplateAssetObjectUrl": "s3://cdk-hnb659fds-assets-954976309477-us-east-1/925d316322d8963570ee998ceaa1f57d02e61018dd3ecb6ea5d802333c77c61e.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version", "additionalDependencies": ["AwsmcpdemoStack.assets"], "lookupRole": {"arn": "arn:${AWS::Partition}:iam::954976309477:role/cdk-hnb659fds-lookup-role-954976309477-us-east-1", "requiresBootstrapStackVersion": 8, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "dependencies": ["AwsmcpdemoStack.assets"], "metadata": {"/AwsmcpdemoStack/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/AwsmcpdemoStack/BootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "BootstrapVersion"}], "/AwsmcpdemoStack/CheckBootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "CheckBootstrapVersion"}]}, "displayName": "AwsmcpdemoStack"}, "CloudFrontImportStack.assets": {"type": "cdk:asset-manifest", "properties": {"file": "CloudFrontImportStack.assets.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "CloudFrontImportStack": {"type": "aws:cloudformation:stack", "environment": "aws://954976309477/us-east-1", "properties": {"templateFile": "CloudFrontImportStack.template.json", "terminationProtection": false, "validateOnSynth": false, "assumeRoleArn": "arn:${AWS::Partition}:iam::954976309477:role/cdk-hnb659fds-deploy-role-954976309477-us-east-1", "cloudFormationExecutionRoleArn": "arn:${AWS::Partition}:iam::954976309477:role/cdk-hnb659fds-cfn-exec-role-954976309477-us-east-1", "stackTemplateAssetObjectUrl": "s3://cdk-hnb659fds-assets-954976309477-us-east-1/1ab1438d10e971b611e3e7192ac3045cf97bba895950f1247bd0b861624f2573.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version", "additionalDependencies": ["CloudFrontImportStack.assets"], "lookupRole": {"arn": "arn:${AWS::Partition}:iam::954976309477:role/cdk-hnb659fds-lookup-role-954976309477-us-east-1", "requiresBootstrapStackVersion": 8, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "dependencies": ["CloudFrontImportStack.assets"], "metadata": {"/CloudFrontImportStack/DistributionDomainName0": [{"type": "aws:cdk:logicalId", "data": "DistributionDomainName0"}], "/CloudFrontImportStack/DistributionId0": [{"type": "aws:cdk:logicalId", "data": "DistributionId0"}], "/CloudFrontImportStack/DistributionArn0": [{"type": "aws:cdk:logicalId", "data": "DistributionArn0"}], "/CloudFrontImportStack/DistributionDomainName1": [{"type": "aws:cdk:logicalId", "data": "DistributionDomainName1"}], "/CloudFrontImportStack/DistributionId1": [{"type": "aws:cdk:logicalId", "data": "DistributionId1"}], "/CloudFrontImportStack/DistributionArn1": [{"type": "aws:cdk:logicalId", "data": "DistributionArn1"}], "/CloudFrontImportStack/DistributionDomainName2": [{"type": "aws:cdk:logicalId", "data": "DistributionDomainName2"}], "/CloudFrontImportStack/DistributionId2": [{"type": "aws:cdk:logicalId", "data": "DistributionId2"}], "/CloudFrontImportStack/DistributionArn2": [{"type": "aws:cdk:logicalId", "data": "DistributionArn2"}], "/CloudFrontImportStack/TotalDistributions": [{"type": "aws:cdk:logicalId", "data": "TotalDistributions"}], "/CloudFrontImportStack/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/CloudFrontImportStack/BootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "BootstrapVersion"}], "/CloudFrontImportStack/CheckBootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "CheckBootstrapVersion"}]}, "displayName": "CloudFrontImportStack"}, "Tree": {"type": "cdk:tree", "properties": {"file": "tree.json"}}}, "minimumCliVersion": "2.1018.1"}