{"version": "tree-0.1", "tree": {"id": "App", "path": "", "constructInfo": {"fqn": "aws-cdk-lib.App", "version": "2.200.1"}, "children": {"AwsmcpdemoStack": {"id": "AwsmcpdemoStack", "path": "AwsmcpdemoStack", "constructInfo": {"fqn": "aws-cdk-lib.<PERSON><PERSON>", "version": "2.200.1"}, "children": {"CDKMetadata": {"id": "CDKMetadata", "path": "AwsmcpdemoStack/CDKMetadata", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}, "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "AwsmcpdemoStack/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.200.1"}}, "Condition": {"id": "Condition", "path": "AwsmcpdemoStack/CDKMetadata/Condition", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.200.1"}}}}, "BootstrapVersion": {"id": "BootstrapVersion", "path": "AwsmcpdemoStack/BootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.200.1"}}, "CheckBootstrapVersion": {"id": "CheckBootstrapVersion", "path": "AwsmcpdemoStack/CheckBootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnRule", "version": "2.200.1"}}}}, "Tree": {"id": "Tree", "path": "Tree", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}}}}