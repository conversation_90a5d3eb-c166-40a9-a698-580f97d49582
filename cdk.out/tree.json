{"version": "tree-0.1", "tree": {"id": "App", "path": "", "constructInfo": {"fqn": "aws-cdk-lib.App", "version": "2.200.1"}, "children": {"AwsmcpdemoStack": {"id": "AwsmcpdemoStack", "path": "AwsmcpdemoStack", "constructInfo": {"fqn": "aws-cdk-lib.<PERSON><PERSON>", "version": "2.200.1"}, "children": {"CDKMetadata": {"id": "CDKMetadata", "path": "AwsmcpdemoStack/CDKMetadata", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}, "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "AwsmcpdemoStack/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.200.1"}}}}, "BootstrapVersion": {"id": "BootstrapVersion", "path": "AwsmcpdemoStack/BootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.200.1"}}, "CheckBootstrapVersion": {"id": "CheckBootstrapVersion", "path": "AwsmcpdemoStack/CheckBootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnRule", "version": "2.200.1"}}}}, "CloudFrontImportStack": {"id": "CloudFrontImportStack", "path": "CloudFrontImportStack", "constructInfo": {"fqn": "aws-cdk-lib.<PERSON><PERSON>", "version": "2.200.1"}, "children": {"ImportedDistribution0": {"id": "ImportedDistribution0", "path": "CloudFrontImportStack/ImportedDistribution0", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.200.1", "metadata": []}}, "DistributionDomainName0": {"id": "DistributionDomainName0", "path": "CloudFrontImportStack/DistributionDomainName0", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "DistributionId0": {"id": "DistributionId0", "path": "CloudFrontImportStack/DistributionId0", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "DistributionArn0": {"id": "DistributionArn0", "path": "CloudFrontImportStack/DistributionArn0", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "ImportedDistribution1": {"id": "ImportedDistribution1", "path": "CloudFrontImportStack/ImportedDistribution1", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.200.1", "metadata": []}}, "DistributionDomainName1": {"id": "DistributionDomainName1", "path": "CloudFrontImportStack/DistributionDomainName1", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "DistributionId1": {"id": "DistributionId1", "path": "CloudFrontImportStack/DistributionId1", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "DistributionArn1": {"id": "DistributionArn1", "path": "CloudFrontImportStack/DistributionArn1", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "ImportedDistribution2": {"id": "ImportedDistribution2", "path": "CloudFrontImportStack/ImportedDistribution2", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.200.1", "metadata": []}}, "DistributionDomainName2": {"id": "DistributionDomainName2", "path": "CloudFrontImportStack/DistributionDomainName2", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "DistributionId2": {"id": "DistributionId2", "path": "CloudFrontImportStack/DistributionId2", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "DistributionArn2": {"id": "DistributionArn2", "path": "CloudFrontImportStack/DistributionArn2", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "TotalDistributions": {"id": "TotalDistributions", "path": "CloudFrontImportStack/TotalDistributions", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.200.1"}}, "CDKMetadata": {"id": "CDKMetadata", "path": "CloudFrontImportStack/CDKMetadata", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}, "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "CloudFrontImportStack/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.200.1"}}}}, "BootstrapVersion": {"id": "BootstrapVersion", "path": "CloudFrontImportStack/BootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.200.1"}}, "CheckBootstrapVersion": {"id": "CheckBootstrapVersion", "path": "CloudFrontImportStack/CheckBootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnRule", "version": "2.200.1"}}}}, "Tree": {"id": "Tree", "path": "Tree", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}}}}