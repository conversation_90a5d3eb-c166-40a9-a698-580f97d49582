#!/bin/bash

# CloudFront Discovery Script
# This script discovers existing CloudFront distributions and generates CDK import configuration

set -e

PROFILE="DevExperimentation-954976309477"
OUTPUT_DIR="docs"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

echo "🔍 Discovering CloudFront distributions..."
echo "Using AWS Profile: $PROFILE"
echo "Output Directory: $OUTPUT_DIR"

# Create output directory if it doesn't exist
mkdir -p "$OUTPUT_DIR"

# Get list of all CloudFront distributions
echo "📋 Fetching CloudFront distributions..."
aws cloudfront list-distributions \
    --profile "$PROFILE" \
    --query 'DistributionList.Items[*].{Id:Id,DomainName:DomainName,Status:Status,Aliases:Aliases.Items,Comment:Comment}' \
    --output table

# Get detailed information for each distribution
echo "📝 Generating detailed configuration..."
aws cloudfront list-distributions \
    --profile "$PROFILE" \
    --query 'DistributionList.Items[*].Id' \
    --output text | tr '\t' '\n' | while read -r DIST_ID; do
    
    if [ -n "$DIST_ID" ]; then
        echo "Processing distribution: $DIST_ID"
        
        # Get distribution configuration
        aws cloudfront get-distribution \
            --id "$DIST_ID" \
            --profile "$PROFILE" \
            --output json > "$OUTPUT_DIR/distribution_${DIST_ID}.json"
        
        # Extract key information
        aws cloudfront get-distribution \
            --id "$DIST_ID" \
            --profile "$PROFILE" \
            --query 'Distribution.{Id:Id,DomainName:DomainName,Status:Status,Aliases:DistributionConfig.Aliases.Items,Origins:DistributionConfig.Origins.Items[*].{Id:Id,DomainName:DomainName,OriginPath:OriginPath},DefaultCacheBehavior:DistributionConfig.DefaultCacheBehavior.{TargetOriginId:TargetOriginId,ViewerProtocolPolicy:ViewerProtocolPolicy},CacheBehaviors:DistributionConfig.CacheBehaviors.Items[*].{PathPattern:PathPattern,TargetOriginId:TargetOriginId}}' \
            --output json > "$OUTPUT_DIR/distribution_${DIST_ID}_summary.json"
    fi
done

echo "✅ Discovery complete!"
echo "📁 Files saved to: $OUTPUT_DIR/"
echo ""
echo "Next steps:"
echo "1. Review the generated files in $OUTPUT_DIR/"
echo "2. Run: npm run generate-import-stack"
echo "3. Deploy the import stack: npx cdk deploy CloudFrontImportStack --profile $PROFILE"
