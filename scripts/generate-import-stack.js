#!/usr/bin/env node

/**
 * Generate CloudFront Import Stack from discovered distributions
 * This script reads the discovered CloudFront configurations and generates
 * a CDK import stack with the actual distribution IDs and domain names.
 */

const fs = require('fs');
const path = require('path');

const DOCS_DIR = 'docs';
const IMPORT_STACK_FILE = 'lib/cloudfront-import-stack.ts';

function main() {
    console.log('🔄 Generating CloudFront import stack from discovered distributions...');
    
    // Read all distribution summary files
    const distributionConfigs = [];
    
    if (!fs.existsSync(DOCS_DIR)) {
        console.error(`❌ Directory ${DOCS_DIR} not found. Run ./scripts/discover-cloudfront.sh first.`);
        process.exit(1);
    }
    
    const files = fs.readdirSync(DOCS_DIR);
    const summaryFiles = files.filter(file => file.endsWith('_summary.json'));
    
    if (summaryFiles.length === 0) {
        console.error(`❌ No distribution summary files found in ${DOCS_DIR}. Run ./scripts/discover-cloudfront.sh first.`);
        process.exit(1);
    }
    
    console.log(`📋 Found ${summaryFiles.length} distribution(s)`);
    
    // Process each distribution
    summaryFiles.forEach(file => {
        const filePath = path.join(DOCS_DIR, file);
        const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        
        const config = {
            id: data.Id,
            domainName: data.DomainName,
            aliases: data.Aliases || [],
            comment: `Imported distribution ${data.Id}`,
            status: data.Status
        };
        
        distributionConfigs.push(config);
        console.log(`  ✓ ${config.id} - ${config.domainName}`);
    });
    
    // Generate the updated import stack
    const stackContent = generateImportStackContent(distributionConfigs);
    
    // Write the updated stack file
    fs.writeFileSync(IMPORT_STACK_FILE, stackContent);
    console.log(`✅ Updated ${IMPORT_STACK_FILE} with ${distributionConfigs.length} distribution(s)`);
    
    // Generate documentation
    generateDocumentation(distributionConfigs);
    
    console.log('🎉 Import stack generation complete!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Build the project: npm run build');
    console.log('2. Synthesize: npx cdk synth CloudFrontImportStack --profile DevExperimentation-************');
    console.log('3. Deploy: npx cdk deploy CloudFrontImportStack --profile DevExperimentation-************');
}

function generateImportStackContent(configs) {
    return `import * as cdk from 'aws-cdk-lib';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as acm from 'aws-cdk-lib/aws-certificatemanager';
import { Construct } from 'constructs';

export interface CloudFrontImportStackProps extends cdk.StackProps {
  readonly distributionConfigs?: DistributionConfig[];
}

export interface DistributionConfig {
  readonly id: string;
  readonly domainName: string;
  readonly aliases?: string[];
  readonly comment?: string;
  readonly status?: string;
}

export class CloudFrontImportStack extends cdk.Stack {
  public readonly importedDistributions: cloudfront.IDistribution[] = [];

  constructor(scope: Construct, id: string, props?: CloudFrontImportStackProps) {
    super(scope, id, props);

    // Import discovered CloudFront distributions
    const discoveredDistributions: DistributionConfig[] = ${JSON.stringify(configs, null, 6)};

    discoveredDistributions.forEach((config, index) => {
      const distribution = cloudfront.Distribution.fromDistributionAttributes(
        this,
        \`ImportedDistribution\${index}\`,
        {
          distributionId: config.id,
          domainName: config.domainName,
        }
      );
      
      this.importedDistributions.push(distribution);

      // Output distribution information
      new cdk.CfnOutput(this, \`DistributionDomainName\${index}\`, {
        value: config.domainName,
        description: \`Domain name for \${config.comment}\`,
      });

      new cdk.CfnOutput(this, \`DistributionId\${index}\`, {
        value: config.id,
        description: \`Distribution ID for \${config.comment}\`,
      });

      new cdk.CfnOutput(this, \`DistributionArn\${index}\`, {
        value: \`arn:aws:cloudfront::************:distribution/\${config.id}\`,
        description: \`ARN for \${config.comment}\`,
      });
    });

    // Summary output
    new cdk.CfnOutput(this, 'TotalDistributions', {
      value: discoveredDistributions.length.toString(),
      description: 'Total number of imported CloudFront distributions',
    });
  }
}`;
}

function generateDocumentation(configs) {
    const docContent = `# Discovered CloudFront Distributions

Generated on: ${new Date().toISOString()}
AWS Account: ************
AWS Profile: DevExperimentation-************

## Summary

Total distributions found: ${configs.length}

## Distributions

${configs.map((config, index) => `
### Distribution ${index + 1}
- **ID**: ${config.id}
- **Domain Name**: ${config.domainName}
- **Status**: ${config.status}
- **Aliases**: ${config.aliases.length > 0 ? config.aliases.join(', ') : 'None'}
- **Comment**: ${config.comment}
`).join('\n')}

## CDK Import Status

✅ **Import Stack Generated**: CloudFrontImportStack
✅ **Distributions Configured**: ${configs.length}
⏳ **Next Step**: Deploy the import stack

## Commands

\`\`\`bash
# Build the project
npm run build

# Synthesize the import stack
npx cdk synth CloudFrontImportStack --profile DevExperimentation-************

# Deploy the import stack (safe - no new resources created)
npx cdk deploy CloudFrontImportStack --profile DevExperimentation-************
\`\`\`

## Files Generated

${configs.map(config => `- docs/distribution_${config.id}.json`).join('\n')}
${configs.map(config => `- docs/distribution_${config.id}_summary.json`).join('\n')}
`;

    fs.writeFileSync(path.join(DOCS_DIR, 'cloudfront-discovery-report.md'), docContent);
    console.log(`📄 Generated documentation: ${DOCS_DIR}/cloudfront-discovery-report.md`);
}

if (require.main === module) {
    main();
}
