# AWS CDK MCP Demo Project

This project demonstrates the integration of AWS CDK with the AWS Labs CDK MCP (Model Context Protocol) server for enhanced development capabilities in VS Code.

## 🔧 AWS Labs CDK MCP Integration

This project leverages the AWS Labs CDK MCP server for enhanced CDK capabilities:

```json
{
  "servers": {
    "awslabs.cdk-mcp-server": {
      "command": "uvx",
      "args": ["awslabs.cdk-mcp-server@latest"],
      "env": {
        "FASTMCP_LOG_LEVEL": "ERROR"
      }
    }
  }
}
```

## 🚀 Quick Start

### Prerequisites
- Node.js and npm installed
- AWS CLI configured
- AWS CDK CLI installed
- VS Code with MCP support
- `uvx` (part of uv package manager)

### Setup
```bash
# Install dependencies
npm install

# Build the project
npm run build

# Synthesize CloudFormation template
npx cdk synth
```

### VS Code MCP Configuration

The project includes a `.vscode/mcp.json` configuration file that automatically sets up the AWS Labs CDK MCP server. This provides:

1. **Enhanced CDK IntelliSense**: Better autocomplete and documentation
2. **AWS Resource Discovery**: Intelligent suggestions for AWS resources
3. **Configuration Validation**: Real-time validation of CDK configurations
4. **Best Practices**: Automated suggestions for CDK best practices

### Using the MCP Server

1. **Open VS Code**: Open this project in VS Code
2. **MCP Integration**: The MCP server will automatically start when VS Code loads
3. **Enhanced Features**: You'll get enhanced CDK development features through the MCP integration

### Testing the Configuration

To verify the MCP server is working:

```bash
# Test the MCP server installation
uvx awslabs.cdk-mcp-server@latest

# Build and synthesize the CDK app
npm run build
npx cdk synth
```

## 📁 Project Structure

```
├── bin/awsmcpdemo.ts                    # CDK app entry point
├── lib/awsmcpdemo-stack.ts              # Main CDK stack
├── .vscode/mcp.json                     # MCP server configuration
├── package.json                         # Dependencies
├── tsconfig.json                        # TypeScript configuration
└── README.md                           # This file
```

## 🛠️ Available Commands

- `npm run build` - Compile TypeScript to JavaScript
- `npm run watch` - Watch for changes and compile
- `npm run test` - Run the Jest unit tests
- `npm run cdk` - Run CDK commands

## 📚 Learn More

- [AWS CDK Documentation](https://docs.aws.amazon.com/cdk/)
- [AWS Labs CDK MCP Server](https://github.com/awslabs/cdk-mcp-server)
- [Model Context Protocol](https://modelcontextprotocol.io/)

## 🔍 Troubleshooting

### MCP Server Issues
```bash
# Check if uvx is installed
which uvx

# Test MCP server installation
uvx awslabs.cdk-mcp-server@latest

# Restart VS Code if needed
```

### CDK Issues
```bash
# Check CDK version
npx cdk --version

# Bootstrap CDK (if needed)
npx cdk bootstrap
```
