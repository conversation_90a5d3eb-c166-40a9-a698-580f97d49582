# AWS CDK MCP Demo Project

This project demonstrates the integration of AWS CDK with the AWS Labs CDK MCP (Model Context Protocol) server for enhanced development capabilities in VS Code.

## 🔧 AWS Labs CDK MCP Integration

This project leverages the AWS Labs CDK MCP server for enhanced CDK capabilities:

```json
{
  "servers": {
    "awslabs.cdk-mcp-server": {
      "command": "uvx",
      "args": ["awslabs.cdk-mcp-server@latest"],
      "env": {
        "FASTMCP_LOG_LEVEL": "ERROR"
      }
    }
  }
}
```

## 🚀 Quick Start

### Prerequisites
- Node.js and npm installed
- AWS CLI configured with SSO profile: `DevExperimentation-954976309477`
- AWS CDK CLI installed
- VS Code with MCP support
- `uvx` (part of uv package manager)

### Setup
```bash
# Install dependencies
npm install

# Build the project
npm run build

# Discover existing CloudFront distributions
npm run discover-cloudfront

# Generate CDK import stack from discovered distributions
npm run generate-import-stack

# Synthesize the import stack
npm run synth-import

# Deploy the import stack (safe - imports existing resources)
npm run deploy-import
```

### VS Code MCP Configuration

The project includes a `.vscode/mcp.json` configuration file that automatically sets up the AWS Labs CDK MCP server. This provides:

1. **Enhanced CDK IntelliSense**: Better autocomplete and documentation
2. **AWS Resource Discovery**: Intelligent suggestions for AWS resources
3. **Configuration Validation**: Real-time validation of CDK configurations
4. **Best Practices**: Automated suggestions for CDK best practices

### Using the MCP Server

1. **Open VS Code**: Open this project in VS Code
2. **MCP Integration**: The MCP server will automatically start when VS Code loads
3. **Enhanced Features**: You'll get enhanced CDK development features through the MCP integration

### Testing the Configuration

To verify the MCP server is working:

```bash
# Test the MCP server installation
uvx awslabs.cdk-mcp-server@latest

# Build and synthesize the CDK app
npm run build
npx cdk synth
```

## 🌐 Discovered CloudFront Distributions

The following CloudFront distributions were discovered in your AWS account:

1. **E1A4N8KHARPVB2** - `d6g1hzg988ekh.cloudfront.net`
   - Alias: `test-form.quotewizard.com`
   - Status: Deployed

2. **E2CKW8QIG4YG9K** - `d1ia0hyamdrxlh.cloudfront.net`
   - Alias: `rubk-test.tree.com`
   - Status: Deployed

3. **E31WE94EAQ0907** - `d2ygag17vkxn4c.cloudfront.net`
   - Alias: `rubk-test.dev.tree.com`
   - Status: Deployed

## 📁 Project Structure

```
├── bin/awsmcpdemo.ts                    # CDK app entry point
├── lib/awsmcpdemo-stack.ts              # Main CDK stack
├── lib/cloudfront-import-stack.ts       # CloudFront import stack
├── scripts/discover-cloudfront.sh       # CloudFront discovery script
├── scripts/generate-import-stack.js     # Import stack generator
├── docs/                                # Generated documentation and configs
├── .vscode/mcp.json                     # MCP server configuration
├── package.json                         # Dependencies
├── tsconfig.json                        # TypeScript configuration
└── README.md                           # This file
```

## 🛠️ Available Commands

- `npm run build` - Compile TypeScript to JavaScript
- `npm run watch` - Watch for changes and compile
- `npm run test` - Run the Jest unit tests
- `npm run cdk` - Run CDK commands
- `npm run discover-cloudfront` - Discover existing CloudFront distributions
- `npm run generate-import-stack` - Generate CDK import stack from discovered distributions
- `npm run synth-import` - Synthesize the CloudFront import stack
- `npm run deploy-import` - Deploy the CloudFront import stack

## 📚 Learn More

- [AWS CDK Documentation](https://docs.aws.amazon.com/cdk/)
- [AWS Labs CDK MCP Server](https://github.com/awslabs/cdk-mcp-server)
- [Model Context Protocol](https://modelcontextprotocol.io/)

## 🔍 Troubleshooting

### MCP Server Issues
```bash
# Check if uvx is installed
which uvx

# Test MCP server installation
uvx awslabs.cdk-mcp-server@latest

# Restart VS Code if needed
```

### CDK Issues
```bash
# Check CDK version
npx cdk --version

# Bootstrap CDK (if needed)
npx cdk bootstrap
```
