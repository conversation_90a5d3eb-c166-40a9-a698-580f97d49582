{"name": "awsmcpdemo", "version": "0.1.0", "bin": {"awsmcpdemo": "bin/awsmcpdemo.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk", "discover-cloudfront": "./scripts/discover-cloudfront.sh", "generate-import-stack": "node scripts/generate-import-stack.js", "deploy-import": "npm run build && npx cdk deploy CloudFrontImportStack --profile DevExperimentation-954976309477", "synth-import": "npm run build && npx cdk synth CloudFrontImportStack --profile DevExperimentation-954976309477"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "22.7.9", "jest": "^29.7.0", "ts-jest": "^29.2.5", "aws-cdk": "2.1018.1", "ts-node": "^10.9.2", "typescript": "~5.6.3"}, "dependencies": {"aws-cdk-lib": "2.200.1", "constructs": "^10.0.0"}}