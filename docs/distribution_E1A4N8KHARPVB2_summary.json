{"Id": "E1A4N8KHARPVB2", "DomainName": "d6g1hzg988ekh.cloudfront.net", "Status": "Deployed", "Aliases": ["test-form.quotewizard.com"], "Origins": [{"Id": "rubkcontent", "DomainName": "dev-rubk-rubkcontent.s3.us-east-1.amazonaws.com", "OriginPath": ""}, {"Id": "kubep_origin", "DomainName": "rubk-kubep.dev.tree.com", "OriginPath": ""}, {"Id": "kube_origin", "DomainName": "rubk-kube.dev.tree.com", "OriginPath": ""}, {"Id": "api_gateway", "DomainName": "4r5j9hdfra.execute-api.us-east-1.amazonaws.com", "OriginPath": "/prod"}, {"Id": "qp_delty", "DomainName": "qp.delty.io", "OriginPath": ""}, {"Id": "tazassets", "DomainName": "dev-rubk-tazassets.s3.us-east-1.amazonaws.com", "OriginPath": ""}, {"Id": "rubkforms", "DomainName": "dev-rubk-rubkforms.s3.us-east-1.amazonaws.com", "OriginPath": ""}, {"Id": "qp_delty_io", "DomainName": "qsakge5gg7.execute-api.us-east-1.amazonaws.com", "OriginPath": "/dev"}], "DefaultCacheBehavior": {"TargetOriginId": "kube_origin", "ViewerProtocolPolicy": "redirect-to-https"}, "CacheBehaviors": [{"PathPattern": "/c/*", "TargetOriginId": "qp_delty_io"}, {"PathPattern": "/sites/*", "TargetOriginId": "rubkcontent"}, {"PathPattern": "/assets/*", "TargetOriginId": "rubkcontent"}, {"PathPattern": "/creative/*", "TargetOriginId": "rubkcontent"}, {"PathPattern": "/includes/*", "TargetOriginId": "rubkcontent"}, {"PathPattern": "/lp/*", "TargetOriginId": "rubkcontent"}, {"PathPattern": "/form/", "TargetOriginId": "kube_origin"}, {"PathPattern": "/form/index.html", "TargetOriginId": "kube_origin"}, {"PathPattern": "/form/*", "TargetOriginId": "rubkforms"}, {"PathPattern": "/home/", "TargetOriginId": "kube_origin"}, {"PathPattern": "/home/<USER>", "TargetOriginId": "kube_origin"}, {"PathPattern": "/home/<USER>", "TargetOriginId": "rubkforms"}, {"PathPattern": "/favicon.ico", "TargetOriginId": "rubkforms"}, {"PathPattern": "/urlshortner/*", "TargetOriginId": "api_gateway"}, {"PathPattern": "/public*", "TargetOriginId": "tazassets"}, {"PathPattern": "/oops/*", "TargetOriginId": "rubkcontent"}, {"PathPattern": "/kube/*", "TargetOriginId": "kube_origin"}, {"PathPattern": "/kubep/*", "TargetOriginId": "kube_origin"}]}