{"ETag": "E375SSN4SNHVX9", "Distribution": {"Id": "E1A4N8KHARPVB2", "ARN": "arn:aws:cloudfront::954976309477:distribution/E1A4N8KHARPVB2", "Status": "Deployed", "LastModifiedTime": "2025-05-12T16:50:10.494000+00:00", "InProgressInvalidationBatches": 0, "DomainName": "d6g1hzg988ekh.cloudfront.net", "ActiveTrustedSigners": {"Enabled": false, "Quantity": 0}, "ActiveTrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "DistributionConfig": {"CallerReference": "terraform-20250108185203878200000001", "Aliases": {"Quantity": 1, "Items": ["test-form.quotewizard.com"]}, "DefaultRootObject": "", "Origins": {"Quantity": 8, "Items": [{"Id": "rubkcontent", "DomainName": "dev-rubk-rubkcontent.s3.us-east-1.amazonaws.com", "OriginPath": "", "CustomHeaders": {"Quantity": 0}, "S3OriginConfig": {"OriginAccessIdentity": ""}, "ConnectionAttempts": 3, "ConnectionTimeout": 10, "OriginShield": {"Enabled": false}, "OriginAccessControlId": "E2DK8UDY8WCPQ6"}, {"Id": "kubep_origin", "DomainName": "rubk-kubep.dev.tree.com", "OriginPath": "", "CustomHeaders": {"Quantity": 0}, "CustomOriginConfig": {"HTTPPort": 80, "HTTPSPort": 443, "OriginProtocolPolicy": "http-only", "OriginSslProtocols": {"Quantity": 1, "Items": ["TLSv1.2"]}, "OriginReadTimeout": 30, "OriginKeepaliveTimeout": 5}, "ConnectionAttempts": 3, "ConnectionTimeout": 10, "OriginShield": {"Enabled": false}, "OriginAccessControlId": ""}, {"Id": "kube_origin", "DomainName": "rubk-kube.dev.tree.com", "OriginPath": "", "CustomHeaders": {"Quantity": 0}, "CustomOriginConfig": {"HTTPPort": 80, "HTTPSPort": 443, "OriginProtocolPolicy": "http-only", "OriginSslProtocols": {"Quantity": 1, "Items": ["TLSv1.2"]}, "OriginReadTimeout": 30, "OriginKeepaliveTimeout": 5}, "ConnectionAttempts": 3, "ConnectionTimeout": 10, "OriginShield": {"Enabled": false}, "OriginAccessControlId": ""}, {"Id": "api_gateway", "DomainName": "4r5j9hdfra.execute-api.us-east-1.amazonaws.com", "OriginPath": "/prod", "CustomHeaders": {"Quantity": 0}, "CustomOriginConfig": {"HTTPPort": 80, "HTTPSPort": 443, "OriginProtocolPolicy": "https-only", "OriginSslProtocols": {"Quantity": 1, "Items": ["TLSv1.2"]}, "OriginReadTimeout": 30, "OriginKeepaliveTimeout": 5}, "ConnectionAttempts": 3, "ConnectionTimeout": 10, "OriginShield": {"Enabled": false}, "OriginAccessControlId": ""}, {"Id": "qp_delty", "DomainName": "qp.delty.io", "OriginPath": "", "CustomHeaders": {"Quantity": 0}, "CustomOriginConfig": {"HTTPPort": 80, "HTTPSPort": 443, "OriginProtocolPolicy": "http-only", "OriginSslProtocols": {"Quantity": 1, "Items": ["TLSv1.2"]}, "OriginReadTimeout": 30, "OriginKeepaliveTimeout": 5}, "ConnectionAttempts": 3, "ConnectionTimeout": 10, "OriginShield": {"Enabled": false}, "OriginAccessControlId": ""}, {"Id": "tazassets", "DomainName": "dev-rubk-tazassets.s3.us-east-1.amazonaws.com", "OriginPath": "", "CustomHeaders": {"Quantity": 0}, "S3OriginConfig": {"OriginAccessIdentity": ""}, "ConnectionAttempts": 3, "ConnectionTimeout": 10, "OriginShield": {"Enabled": false}, "OriginAccessControlId": "E2DK8UDY8WCPQ6"}, {"Id": "rubkforms", "DomainName": "dev-rubk-rubkforms.s3.us-east-1.amazonaws.com", "OriginPath": "", "CustomHeaders": {"Quantity": 0}, "S3OriginConfig": {"OriginAccessIdentity": ""}, "ConnectionAttempts": 3, "ConnectionTimeout": 10, "OriginShield": {"Enabled": false}, "OriginAccessControlId": "E2DK8UDY8WCPQ6"}, {"Id": "qp_delty_io", "DomainName": "qsakge5gg7.execute-api.us-east-1.amazonaws.com", "OriginPath": "/dev", "CustomHeaders": {"Quantity": 0}, "CustomOriginConfig": {"HTTPPort": 80, "HTTPSPort": 443, "OriginProtocolPolicy": "https-only", "OriginSslProtocols": {"Quantity": 1, "Items": ["TLSv1.2"]}, "OriginReadTimeout": 30, "OriginKeepaliveTimeout": 5}, "ConnectionAttempts": 3, "ConnectionTimeout": 10, "OriginShield": {"Enabled": false}, "OriginAccessControlId": ""}]}, "OriginGroups": {"Quantity": 0}, "DefaultCacheBehavior": {"TargetOriginId": "kube_origin", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 7, "Items": ["HEAD", "DELETE", "POST", "GET", "OPTIONS", "PUT", "PATCH"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/kube_path_rewrite_header", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "CachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "OriginRequestPolicyId": "7f99fbcb-eae1-478c-aef1-f876471f4969", "GrpcConfig": {"Enabled": false}}, "CacheBehaviors": {"Quantity": 18, "Items": [{"PathPattern": "/c/*", "TargetOriginId": "qp_delty_io", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 3, "Items": ["HEAD", "GET", "OPTIONS"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/qp_delty_io", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "CachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "OriginRequestPolicyId": "7f99fbcb-eae1-478c-aef1-f876471f4969", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/sites/*", "TargetOriginId": "rubkcontent", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 3, "Items": ["HEAD", "GET", "OPTIONS"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 0}, "FieldLevelEncryptionId": "", "CachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6", "OriginRequestPolicyId": "88a5eaf4-2fd4-4709-b370-b4c650ea3fcf", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/assets/*", "TargetOriginId": "rubkcontent", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 3, "Items": ["HEAD", "GET", "OPTIONS"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 0}, "FieldLevelEncryptionId": "", "CachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6", "OriginRequestPolicyId": "88a5eaf4-2fd4-4709-b370-b4c650ea3fcf", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/creative/*", "TargetOriginId": "rubkcontent", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 3, "Items": ["HEAD", "GET", "OPTIONS"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/creative_path_rewrite", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "CachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6", "OriginRequestPolicyId": "88a5eaf4-2fd4-4709-b370-b4c650ea3fcf", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/includes/*", "TargetOriginId": "rubkcontent", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 3, "Items": ["HEAD", "GET", "OPTIONS"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 0}, "FieldLevelEncryptionId": "", "CachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6", "OriginRequestPolicyId": "88a5eaf4-2fd4-4709-b370-b4c650ea3fcf", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/lp/*", "TargetOriginId": "rubkcontent", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 7, "Items": ["HEAD", "DELETE", "POST", "GET", "OPTIONS", "PUT", "PATCH"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/lptolps_path_rewrite", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "CachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6", "OriginRequestPolicyId": "88a5eaf4-2fd4-4709-b370-b4c650ea3fcf", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/form/", "TargetOriginId": "kube_origin", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 3, "Items": ["HEAD", "GET", "OPTIONS"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/kube_path_rewrite_header", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "CachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "OriginRequestPolicyId": "7f99fbcb-eae1-478c-aef1-f876471f4969", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/form/index.html", "TargetOriginId": "kube_origin", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 7, "Items": ["HEAD", "DELETE", "POST", "GET", "OPTIONS", "PUT", "PATCH"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/kube_path_rewrite_header", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "CachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "OriginRequestPolicyId": "7f99fbcb-eae1-478c-aef1-f876471f4969", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/form/*", "TargetOriginId": "rubkforms", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 3, "Items": ["HEAD", "GET", "OPTIONS"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/form_path_rewrite", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "CachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6", "OriginRequestPolicyId": "88a5eaf4-2fd4-4709-b370-b4c650ea3fcf", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/home/", "TargetOriginId": "kube_origin", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 3, "Items": ["HEAD", "GET", "OPTIONS"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/kube_path_rewrite_header", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "CachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "OriginRequestPolicyId": "7f99fbcb-eae1-478c-aef1-f876471f4969", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/home/<USER>", "TargetOriginId": "kube_origin", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 7, "Items": ["HEAD", "DELETE", "POST", "GET", "OPTIONS", "PUT", "PATCH"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/kube_path_rewrite_header", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "CachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "OriginRequestPolicyId": "7f99fbcb-eae1-478c-aef1-f876471f4969", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/home/<USER>", "TargetOriginId": "rubkforms", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 3, "Items": ["HEAD", "GET", "OPTIONS"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/form_path_rewrite", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "CachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6", "OriginRequestPolicyId": "88a5eaf4-2fd4-4709-b370-b4c650ea3fcf", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/favicon.ico", "TargetOriginId": "rubkforms", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 3, "Items": ["HEAD", "GET", "OPTIONS"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 0}, "FieldLevelEncryptionId": "", "CachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6", "OriginRequestPolicyId": "88a5eaf4-2fd4-4709-b370-b4c650ea3fcf", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/urlshortner/*", "TargetOriginId": "api_gateway", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 7, "Items": ["HEAD", "DELETE", "POST", "GET", "OPTIONS", "PUT", "PATCH"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 0}, "FieldLevelEncryptionId": "", "CachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6", "OriginRequestPolicyId": "216adef6-5c7f-47e4-b989-5492eafa07d3", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/public*", "TargetOriginId": "tazassets", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 3, "Items": ["HEAD", "GET", "OPTIONS"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 0}, "FieldLevelEncryptionId": "", "CachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6", "OriginRequestPolicyId": "88a5eaf4-2fd4-4709-b370-b4c650ea3fcf", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/oops/*", "TargetOriginId": "rubkcontent", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 3, "Items": ["HEAD", "GET", "OPTIONS"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/oops_path_rewrite", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "CachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6", "OriginRequestPolicyId": "88a5eaf4-2fd4-4709-b370-b4c650ea3fcf", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/kube/*", "TargetOriginId": "kube_origin", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 7, "Items": ["HEAD", "DELETE", "POST", "GET", "OPTIONS", "PUT", "PATCH"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/kube_path_rewrite_header", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "CachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "OriginRequestPolicyId": "7f99fbcb-eae1-478c-aef1-f876471f4969", "ResponseHeadersPolicyId": "8b22098c-40dd-4332-a243-b79c3f0afd9a", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/kubep/*", "TargetOriginId": "kube_origin", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 7, "Items": ["HEAD", "DELETE", "POST", "GET", "OPTIONS", "PUT", "PATCH"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/kube_path_rewrite_header", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "CachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "OriginRequestPolicyId": "7f99fbcb-eae1-478c-aef1-f876471f4969", "GrpcConfig": {"Enabled": false}}]}, "CustomErrorResponses": {"Quantity": 0}, "Comment": "", "Logging": {"Enabled": true, "IncludeCookies": false, "Bucket": "tree-publisher-dev-ue1-rubk-access-logs.s3.amazonaws.com", "Prefix": "v1/test-form.quotewizard.com/"}, "PriceClass": "PriceClass_All", "Enabled": true, "ViewerCertificate": {"CloudFrontDefaultCertificate": false, "ACMCertificateArn": "arn:aws:acm:us-east-1:954976309477:certificate/9917b0be-e631-4a97-806f-84a486e8a88c", "SSLSupportMethod": "sni-only", "MinimumProtocolVersion": "TLSv1.2_2018", "Certificate": "arn:aws:acm:us-east-1:954976309477:certificate/9917b0be-e631-4a97-806f-84a486e8a88c", "CertificateSource": "acm"}, "Restrictions": {"GeoRestriction": {"RestrictionType": "whitelist", "Quantity": 22, "Items": ["CA", "JP", "ID", "KR", "US", "GB", "MT", "VI", "PH", "IN", "SG", "IE", "FR", "PR", "MP", "AU", "JM", "GU", "BG", "MX", "AS", "BR"]}}, "WebACLId": "arn:aws:wafv2:us-east-1:954976309477:global/webacl/CloudFrontACLBlock/cb0b9c04-e596-4ed0-b604-a68a78167832", "HttpVersion": "http2", "IsIPV6Enabled": true, "ContinuousDeploymentPolicyId": "", "Staging": false}, "AliasICPRecordals": [{"CNAME": "test-form.quotewizard.com", "ICPRecordalStatus": "APPROVED"}]}}