# Discovered CloudFront Distributions

Generated on: 2025-06-14T04:45:30.266Z
AWS Account: ************
AWS Profile: DevExperimentation-************

## Summary

Total distributions found: 3

## Distributions


### Distribution 1
- **ID**: E1A4N8KHARPVB2
- **Domain Name**: d6g1hzg988ekh.cloudfront.net
- **Status**: Deployed
- **Aliases**: test-form.quotewizard.com
- **Comment**: Imported distribution E1A4N8KHARPVB2


### Distribution 2
- **ID**: E2CKW8QIG4YG9K
- **Domain Name**: d1ia0hyamdrxlh.cloudfront.net
- **Status**: Deployed
- **Aliases**: rubk-test.tree.com
- **Comment**: Imported distribution E2CKW8QIG4YG9K


### Distribution 3
- **ID**: E31WE94EAQ0907
- **Domain Name**: d2ygag17vkxn4c.cloudfront.net
- **Status**: Deployed
- **Aliases**: rubk-test.dev.tree.com
- **Comment**: Imported distribution E31WE94EAQ0907


## CDK Import Status

✅ **Import Stack Generated**: CloudFrontImportStack
✅ **Distributions Configured**: 3
⏳ **Next Step**: Deploy the import stack

## Commands

```bash
# Build the project
npm run build

# Synthesize the import stack
npx cdk synth CloudFrontImportStack --profile DevExperimentation-************

# Deploy the import stack (safe - no new resources created)
npx cdk deploy CloudFrontImportStack --profile DevExperimentation-************
```

## Files Generated

- docs/distribution_E1A4N8KHARPVB2.json
- docs/distribution_E2CKW8QIG4YG9K.json
- docs/distribution_E31WE94EAQ0907.json
- docs/distribution_E1A4N8KHARPVB2_summary.json
- docs/distribution_E2CKW8QIG4YG9K_summary.json
- docs/distribution_E31WE94EAQ0907_summary.json
