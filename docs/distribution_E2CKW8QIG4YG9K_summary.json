{"Id": "E2CKW8QIG4YG9K", "DomainName": "d1ia0hyamdrxlh.cloudfront.net", "Status": "Deployed", "Aliases": ["rubk-test.tree.com"], "Origins": [{"Id": "qp.delty.io", "DomainName": "qp.delty.io", "OriginPath": ""}, {"Id": "kubetest.rubk-test.tree.com", "DomainName": "kubetest.rubk-test.tree.com", "OriginPath": ""}, {"Id": "S3Origin", "DomainName": "dev-lt-publisher-poc-web.s3.us-east-1.amazonaws.com", "OriginPath": ""}, {"Id": "rubkcontent-test.s3.us-east-1.amazonaws.com", "DomainName": "rubkcontent-test.s3.us-east-1.amazonaws.com", "OriginPath": ""}, {"Id": "rubkwebprod.blob.core.windows.net", "DomainName": "rubkwebprod.blob.core.windows.net", "OriginPath": "/prodforms"}, {"Id": "kubeptest.rubk-test.tree.com", "DomainName": "kubeptest.rubk-test.tree.com", "OriginPath": ""}, {"Id": "kahlodevforms.blob.core.windows.net", "DomainName": "kahlodevforms.blob.core.windows.net", "OriginPath": ""}, {"Id": "tazassets-test.s3.us-east-1.amazonaws.com", "DomainName": "tazassets-test.s3.us-east-1.amazonaws.com", "OriginPath": ""}, {"Id": "4r5j9hdfra.execute-api.us-east-1.amazonaws.com", "DomainName": "4r5j9hdfra.execute-api.us-east-1.amazonaws.com", "OriginPath": "/prod"}], "DefaultCacheBehavior": {"TargetOriginId": "kubetest.rubk-test.tree.com", "ViewerProtocolPolicy": "redirect-to-https"}, "CacheBehaviors": [{"PathPattern": "/sites/*", "TargetOriginId": "rubkcontent-test.s3.us-east-1.amazonaws.com"}, {"PathPattern": "/assets/*", "TargetOriginId": "rubkcontent-test.s3.us-east-1.amazonaws.com"}, {"PathPattern": "/creative/*", "TargetOriginId": "rubkcontent-test.s3.us-east-1.amazonaws.com"}, {"PathPattern": "/includes/*", "TargetOriginId": "rubkcontent-test.s3.us-east-1.amazonaws.com"}, {"PathPattern": "/lp/*", "TargetOriginId": "rubkcontent-test.s3.us-east-1.amazonaws.com"}, {"PathPattern": "/form/", "TargetOriginId": "kubetest.rubk-test.tree.com"}, {"PathPattern": "/form/index.html", "TargetOriginId": "kubetest.rubk-test.tree.com"}, {"PathPattern": "/form/*", "TargetOriginId": "kahlodevforms.blob.core.windows.net"}, {"PathPattern": "/home/", "TargetOriginId": "kubetest.rubk-test.tree.com"}, {"PathPattern": "/home/<USER>", "TargetOriginId": "kubetest.rubk-test.tree.com"}, {"PathPattern": "/home/<USER>", "TargetOriginId": "kahlodevforms.blob.core.windows.net"}, {"PathPattern": "/favicon.ico", "TargetOriginId": "kahlodevforms.blob.core.windows.net"}, {"PathPattern": "/urlshortner/*", "TargetOriginId": "4r5j9hdfra.execute-api.us-east-1.amazonaws.com"}, {"PathPattern": "/public*", "TargetOriginId": "tazassets-test.s3.us-east-1.amazonaws.com"}, {"PathPattern": "/oops*", "TargetOriginId": "rubkcontent-test.s3.us-east-1.amazonaws.com"}, {"PathPattern": "/kube/*", "TargetOriginId": "kubetest.rubk-test.tree.com"}, {"PathPattern": "/kubep/*", "TargetOriginId": "kubeptest.rubk-test.tree.com"}]}