{"ETag": "E3CXVMA65MUPPO", "Distribution": {"Id": "E2CKW8QIG4YG9K", "ARN": "arn:aws:cloudfront::954976309477:distribution/E2CKW8QIG4YG9K", "Status": "Deployed", "LastModifiedTime": "2024-12-20T15:15:44.321000+00:00", "InProgressInvalidationBatches": 0, "DomainName": "d1ia0hyamdrxlh.cloudfront.net", "ActiveTrustedSigners": {"Enabled": false, "Quantity": 0}, "ActiveTrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "DistributionConfig": {"CallerReference": "terraform-20241209145159418000000003", "Aliases": {"Quantity": 1, "Items": ["rubk-test.tree.com"]}, "DefaultRootObject": "", "Origins": {"Quantity": 9, "Items": [{"Id": "qp.delty.io", "DomainName": "qp.delty.io", "OriginPath": "", "CustomHeaders": {"Quantity": 0}, "CustomOriginConfig": {"HTTPPort": 80, "HTTPSPort": 443, "OriginProtocolPolicy": "https-only", "OriginSslProtocols": {"Quantity": 1, "Items": ["TLSv1.2"]}, "OriginReadTimeout": 30, "OriginKeepaliveTimeout": 5}, "ConnectionAttempts": 3, "ConnectionTimeout": 10, "OriginShield": {"Enabled": false}, "OriginAccessControlId": ""}, {"Id": "kubetest.rubk-test.tree.com", "DomainName": "kubetest.rubk-test.tree.com", "OriginPath": "", "CustomHeaders": {"Quantity": 0}, "CustomOriginConfig": {"HTTPPort": 80, "HTTPSPort": 443, "OriginProtocolPolicy": "http-only", "OriginSslProtocols": {"Quantity": 4, "Items": ["SSLv3", "TLSv1", "TLSv1.1", "TLSv1.2"]}, "OriginReadTimeout": 30, "OriginKeepaliveTimeout": 5}, "ConnectionAttempts": 3, "ConnectionTimeout": 10, "OriginShield": {"Enabled": false}, "OriginAccessControlId": ""}, {"Id": "S3Origin", "DomainName": "dev-lt-publisher-poc-web.s3.us-east-1.amazonaws.com", "OriginPath": "", "CustomHeaders": {"Quantity": 0}, "S3OriginConfig": {"OriginAccessIdentity": ""}, "ConnectionAttempts": 3, "ConnectionTimeout": 10, "OriginShield": {"Enabled": false}, "OriginAccessControlId": "E2DK8UDY8WCPQ6"}, {"Id": "rubkcontent-test.s3.us-east-1.amazonaws.com", "DomainName": "rubkcontent-test.s3.us-east-1.amazonaws.com", "OriginPath": "", "CustomHeaders": {"Quantity": 0}, "S3OriginConfig": {"OriginAccessIdentity": ""}, "ConnectionAttempts": 3, "ConnectionTimeout": 10, "OriginShield": {"Enabled": false}, "OriginAccessControlId": "ET439Y5AAE6P8"}, {"Id": "rubkwebprod.blob.core.windows.net", "DomainName": "rubkwebprod.blob.core.windows.net", "OriginPath": "/prodforms", "CustomHeaders": {"Quantity": 0}, "CustomOriginConfig": {"HTTPPort": 80, "HTTPSPort": 443, "OriginProtocolPolicy": "https-only", "OriginSslProtocols": {"Quantity": 1, "Items": ["TLSv1.2"]}, "OriginReadTimeout": 30, "OriginKeepaliveTimeout": 5}, "ConnectionAttempts": 3, "ConnectionTimeout": 10, "OriginShield": {"Enabled": false}, "OriginAccessControlId": ""}, {"Id": "kubeptest.rubk-test.tree.com", "DomainName": "kubeptest.rubk-test.tree.com", "OriginPath": "", "CustomHeaders": {"Quantity": 0}, "CustomOriginConfig": {"HTTPPort": 80, "HTTPSPort": 443, "OriginProtocolPolicy": "http-only", "OriginSslProtocols": {"Quantity": 4, "Items": ["SSLv3", "TLSv1", "TLSv1.1", "TLSv1.2"]}, "OriginReadTimeout": 30, "OriginKeepaliveTimeout": 5}, "ConnectionAttempts": 3, "ConnectionTimeout": 10, "OriginShield": {"Enabled": false}, "OriginAccessControlId": ""}, {"Id": "kahlodevforms.blob.core.windows.net", "DomainName": "kahlodevforms.blob.core.windows.net", "OriginPath": "", "CustomHeaders": {"Quantity": 0}, "CustomOriginConfig": {"HTTPPort": 80, "HTTPSPort": 443, "OriginProtocolPolicy": "https-only", "OriginSslProtocols": {"Quantity": 1, "Items": ["TLSv1.2"]}, "OriginReadTimeout": 30, "OriginKeepaliveTimeout": 5}, "ConnectionAttempts": 3, "ConnectionTimeout": 10, "OriginShield": {"Enabled": false}, "OriginAccessControlId": ""}, {"Id": "tazassets-test.s3.us-east-1.amazonaws.com", "DomainName": "tazassets-test.s3.us-east-1.amazonaws.com", "OriginPath": "", "CustomHeaders": {"Quantity": 0}, "S3OriginConfig": {"OriginAccessIdentity": ""}, "ConnectionAttempts": 3, "ConnectionTimeout": 10, "OriginShield": {"Enabled": false}, "OriginAccessControlId": "E366W6VCIWD9EG"}, {"Id": "4r5j9hdfra.execute-api.us-east-1.amazonaws.com", "DomainName": "4r5j9hdfra.execute-api.us-east-1.amazonaws.com", "OriginPath": "/prod", "CustomHeaders": {"Quantity": 0}, "CustomOriginConfig": {"HTTPPort": 80, "HTTPSPort": 443, "OriginProtocolPolicy": "https-only", "OriginSslProtocols": {"Quantity": 1, "Items": ["TLSv1.2"]}, "OriginReadTimeout": 30, "OriginKeepaliveTimeout": 5}, "ConnectionAttempts": 3, "ConnectionTimeout": 10, "OriginShield": {"Enabled": false}, "OriginAccessControlId": ""}]}, "OriginGroups": {"Quantity": 0}, "DefaultCacheBehavior": {"TargetOriginId": "kubetest.rubk-test.tree.com", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 7, "Items": ["HEAD", "DELETE", "POST", "GET", "OPTIONS", "PUT", "PATCH"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/test-rubk", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "CachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "OriginRequestPolicyId": "7f99fbcb-eae1-478c-aef1-f876471f4969", "GrpcConfig": {"Enabled": false}}, "CacheBehaviors": {"Quantity": 17, "Items": [{"PathPattern": "/sites/*", "TargetOriginId": "rubkcontent-test.s3.us-east-1.amazonaws.com", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 0}, "FieldLevelEncryptionId": "", "GrpcConfig": {"Enabled": false}, "ForwardedValues": {"QueryString": false, "Cookies": {"Forward": "none"}, "Headers": {"Quantity": 0}, "QueryStringCacheKeys": {"Quantity": 0}}, "MinTTL": 0, "DefaultTTL": 86400, "MaxTTL": 31536000}, {"PathPattern": "/assets/*", "TargetOriginId": "rubkcontent-test.s3.us-east-1.amazonaws.com", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 0}, "FieldLevelEncryptionId": "", "GrpcConfig": {"Enabled": false}, "ForwardedValues": {"QueryString": false, "Cookies": {"Forward": "none"}, "Headers": {"Quantity": 0}, "QueryStringCacheKeys": {"Quantity": 0}}, "MinTTL": 0, "DefaultTTL": 86400, "MaxTTL": 31536000}, {"PathPattern": "/creative/*", "TargetOriginId": "rubkcontent-test.s3.us-east-1.amazonaws.com", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/rewritecreativetoassets", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "GrpcConfig": {"Enabled": false}, "ForwardedValues": {"QueryString": false, "Cookies": {"Forward": "none"}, "Headers": {"Quantity": 0}, "QueryStringCacheKeys": {"Quantity": 0}}, "MinTTL": 0, "DefaultTTL": 86400, "MaxTTL": 31536000}, {"PathPattern": "/includes/*", "TargetOriginId": "rubkcontent-test.s3.us-east-1.amazonaws.com", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 0}, "FieldLevelEncryptionId": "", "GrpcConfig": {"Enabled": false}, "ForwardedValues": {"QueryString": false, "Cookies": {"Forward": "none"}, "Headers": {"Quantity": 0}, "QueryStringCacheKeys": {"Quantity": 0}}, "MinTTL": 0, "DefaultTTL": 86400, "MaxTTL": 31536000}, {"PathPattern": "/lp/*", "TargetOriginId": "rubkcontent-test.s3.us-east-1.amazonaws.com", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/rewritelptolps", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "GrpcConfig": {"Enabled": false}, "ForwardedValues": {"QueryString": false, "Cookies": {"Forward": "none"}, "Headers": {"Quantity": 0}, "QueryStringCacheKeys": {"Quantity": 0}}, "MinTTL": 0, "DefaultTTL": 86400, "MaxTTL": 31536000}, {"PathPattern": "/form/", "TargetOriginId": "kubetest.rubk-test.tree.com", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 7, "Items": ["HEAD", "DELETE", "POST", "GET", "OPTIONS", "PUT", "PATCH"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/test-rubk", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "CachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "OriginRequestPolicyId": "7f99fbcb-eae1-478c-aef1-f876471f4969", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/form/index.html", "TargetOriginId": "kubetest.rubk-test.tree.com", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 7, "Items": ["HEAD", "DELETE", "POST", "GET", "OPTIONS", "PUT", "PATCH"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/test-rubk", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "CachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "OriginRequestPolicyId": "7f99fbcb-eae1-478c-aef1-f876471f4969", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/form/*", "TargetOriginId": "kahlodevforms.blob.core.windows.net", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 7, "Items": ["HEAD", "DELETE", "POST", "GET", "OPTIONS", "PUT", "PATCH"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/rewritetoform", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "CachePolicyId": "106afdb0-fae1-4061-aabb-6dafef03716e", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/home/", "TargetOriginId": "kubetest.rubk-test.tree.com", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 7, "Items": ["HEAD", "DELETE", "POST", "GET", "OPTIONS", "PUT", "PATCH"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/test-rubk", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "CachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "OriginRequestPolicyId": "7f99fbcb-eae1-478c-aef1-f876471f4969", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/home/<USER>", "TargetOriginId": "kubetest.rubk-test.tree.com", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 7, "Items": ["HEAD", "DELETE", "POST", "GET", "OPTIONS", "PUT", "PATCH"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/test-rubk", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "CachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "OriginRequestPolicyId": "7f99fbcb-eae1-478c-aef1-f876471f4969", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/home/<USER>", "TargetOriginId": "kahlodevforms.blob.core.windows.net", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 7, "Items": ["HEAD", "DELETE", "POST", "GET", "OPTIONS", "PUT", "PATCH"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/rewritetoform", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "GrpcConfig": {"Enabled": false}, "ForwardedValues": {"QueryString": false, "Cookies": {"Forward": "none"}, "Headers": {"Quantity": 0}, "QueryStringCacheKeys": {"Quantity": 0}}, "MinTTL": 0, "DefaultTTL": 86400, "MaxTTL": 31536000}, {"PathPattern": "/favicon.ico", "TargetOriginId": "kahlodevforms.blob.core.windows.net", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 7, "Items": ["HEAD", "DELETE", "POST", "GET", "OPTIONS", "PUT", "PATCH"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 0}, "FieldLevelEncryptionId": "", "GrpcConfig": {"Enabled": false}, "ForwardedValues": {"QueryString": false, "Cookies": {"Forward": "none"}, "Headers": {"Quantity": 0}, "QueryStringCacheKeys": {"Quantity": 0}}, "MinTTL": 0, "DefaultTTL": 86400, "MaxTTL": 31536000}, {"PathPattern": "/urlshortner/*", "TargetOriginId": "4r5j9hdfra.execute-api.us-east-1.amazonaws.com", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 7, "Items": ["HEAD", "DELETE", "POST", "GET", "OPTIONS", "PUT", "PATCH"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 0}, "FieldLevelEncryptionId": "", "GrpcConfig": {"Enabled": false}, "ForwardedValues": {"QueryString": true, "Cookies": {"Forward": "all"}, "Headers": {"Quantity": 0}, "QueryStringCacheKeys": {"Quantity": 0}}, "MinTTL": 0, "DefaultTTL": 86400, "MaxTTL": 31536000}, {"PathPattern": "/public*", "TargetOriginId": "tazassets-test.s3.us-east-1.amazonaws.com", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 0}, "FieldLevelEncryptionId": "", "GrpcConfig": {"Enabled": false}, "ForwardedValues": {"QueryString": false, "Cookies": {"Forward": "none"}, "Headers": {"Quantity": 0}, "QueryStringCacheKeys": {"Quantity": 0}}, "MinTTL": 0, "DefaultTTL": 86400, "MaxTTL": 31536000}, {"PathPattern": "/oops*", "TargetOriginId": "rubkcontent-test.s3.us-east-1.amazonaws.com", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/rewriteoopstopath", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "GrpcConfig": {"Enabled": false}, "ForwardedValues": {"QueryString": false, "Cookies": {"Forward": "none"}, "Headers": {"Quantity": 0}, "QueryStringCacheKeys": {"Quantity": 0}}, "MinTTL": 0, "DefaultTTL": 86400, "MaxTTL": 31536000}, {"PathPattern": "/kube/*", "TargetOriginId": "kubetest.rubk-test.tree.com", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 7, "Items": ["HEAD", "DELETE", "POST", "GET", "OPTIONS", "PUT", "PATCH"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/test-rubk", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "CachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "OriginRequestPolicyId": "7f99fbcb-eae1-478c-aef1-f876471f4969", "GrpcConfig": {"Enabled": false}}, {"PathPattern": "/kubep/*", "TargetOriginId": "kubeptest.rubk-test.tree.com", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 7, "Items": ["HEAD", "DELETE", "POST", "GET", "OPTIONS", "PUT", "PATCH"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::954976309477:function/test-rubk", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "CachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "OriginRequestPolicyId": "7f99fbcb-eae1-478c-aef1-f876471f4969", "GrpcConfig": {"Enabled": false}}]}, "CustomErrorResponses": {"Quantity": 0}, "Comment": "", "Logging": {"Enabled": false, "IncludeCookies": false, "Bucket": "", "Prefix": ""}, "PriceClass": "PriceClass_All", "Enabled": true, "ViewerCertificate": {"CloudFrontDefaultCertificate": false, "ACMCertificateArn": "arn:aws:acm:us-east-1:954976309477:certificate/9bd1cec6-5339-4839-a050-8e68b0869d30", "SSLSupportMethod": "sni-only", "MinimumProtocolVersion": "TLSv1.2_2018", "Certificate": "arn:aws:acm:us-east-1:954976309477:certificate/9bd1cec6-5339-4839-a050-8e68b0869d30", "CertificateSource": "acm"}, "Restrictions": {"GeoRestriction": {"RestrictionType": "whitelist", "Quantity": 4, "Items": ["CA", "US", "GB", "DE"]}}, "WebACLId": "", "HttpVersion": "http2", "IsIPV6Enabled": true, "ContinuousDeploymentPolicyId": "", "Staging": false}, "AliasICPRecordals": [{"CNAME": "rubk-test.tree.com", "ICPRecordalStatus": "APPROVED"}]}}