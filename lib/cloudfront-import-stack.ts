import * as cdk from 'aws-cdk-lib';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as acm from 'aws-cdk-lib/aws-certificatemanager';
import { Construct } from 'constructs';

export interface CloudFrontImportStackProps extends cdk.StackProps {
  readonly distributionConfigs?: DistributionConfig[];
}

export interface DistributionConfig {
  readonly id: string;
  readonly domainName: string;
  readonly aliases?: string[];
  readonly comment?: string;
  readonly status?: string;
}

export class CloudFrontImportStack extends cdk.Stack {
  public readonly importedDistributions: cloudfront.IDistribution[] = [];

  constructor(scope: Construct, id: string, props?: CloudFrontImportStackProps) {
    super(scope, id, props);

    // Import discovered CloudFront distributions
    const discoveredDistributions: DistributionConfig[] = [
      {
            "id": "E1A4N8KHARPVB2",
            "domainName": "d6g1hzg988ekh.cloudfront.net",
            "aliases": [
                  "test-form.quotewizard.com"
            ],
            "comment": "Imported distribution E1A4N8KHARPVB2",
            "status": "Deployed"
      },
      {
            "id": "E2CKW8QIG4YG9K",
            "domainName": "d1ia0hyamdrxlh.cloudfront.net",
            "aliases": [
                  "rubk-test.tree.com"
            ],
            "comment": "Imported distribution E2CKW8QIG4YG9K",
            "status": "Deployed"
      },
      {
            "id": "E31WE94EAQ0907",
            "domainName": "d2ygag17vkxn4c.cloudfront.net",
            "aliases": [
                  "rubk-test.dev.tree.com"
            ],
            "comment": "Imported distribution E31WE94EAQ0907",
            "status": "Deployed"
      }
];

    discoveredDistributions.forEach((config, index) => {
      const distribution = cloudfront.Distribution.fromDistributionAttributes(
        this,
        `ImportedDistribution${index}`,
        {
          distributionId: config.id,
          domainName: config.domainName,
        }
      );
      
      this.importedDistributions.push(distribution);

      // Output distribution information
      new cdk.CfnOutput(this, `DistributionDomainName${index}`, {
        value: config.domainName,
        description: `Domain name for ${config.comment}`,
      });

      new cdk.CfnOutput(this, `DistributionId${index}`, {
        value: config.id,
        description: `Distribution ID for ${config.comment}`,
      });

      new cdk.CfnOutput(this, `DistributionArn${index}`, {
        value: `arn:aws:cloudfront::954976309477:distribution/${config.id}`,
        description: `ARN for ${config.comment}`,
      });
    });

    // Summary output
    new cdk.CfnOutput(this, 'TotalDistributions', {
      value: discoveredDistributions.length.toString(),
      description: 'Total number of imported CloudFront distributions',
    });
  }
}