import * as cdk from 'aws-cdk-lib';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import { Construct } from 'constructs';
export interface CloudFrontImportStackProps extends cdk.StackProps {
    readonly distributionConfigs?: DistributionConfig[];
}
export interface DistributionConfig {
    readonly id: string;
    readonly domainName: string;
    readonly aliases?: string[];
    readonly comment?: string;
    readonly status?: string;
}
export declare class CloudFrontImportStack extends cdk.Stack {
    readonly importedDistributions: cloudfront.IDistribution[];
    constructor(scope: Construct, id: string, props?: CloudFrontImportStackProps);
}
