"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CloudFrontImportStack = void 0;
const cdk = __importStar(require("aws-cdk-lib"));
const cloudfront = __importStar(require("aws-cdk-lib/aws-cloudfront"));
class CloudFrontImportStack extends cdk.Stack {
    importedDistributions = [];
    constructor(scope, id, props) {
        super(scope, id, props);
        // Import discovered CloudFront distributions
        const discoveredDistributions = [
            {
                "id": "E1A4N8KHARPVB2",
                "domainName": "d6g1hzg988ekh.cloudfront.net",
                "aliases": [
                    "test-form.quotewizard.com"
                ],
                "comment": "Imported distribution E1A4N8KHARPVB2",
                "status": "Deployed"
            },
            {
                "id": "E2CKW8QIG4YG9K",
                "domainName": "d1ia0hyamdrxlh.cloudfront.net",
                "aliases": [
                    "rubk-test.tree.com"
                ],
                "comment": "Imported distribution E2CKW8QIG4YG9K",
                "status": "Deployed"
            },
            {
                "id": "E31WE94EAQ0907",
                "domainName": "d2ygag17vkxn4c.cloudfront.net",
                "aliases": [
                    "rubk-test.dev.tree.com"
                ],
                "comment": "Imported distribution E31WE94EAQ0907",
                "status": "Deployed"
            }
        ];
        discoveredDistributions.forEach((config, index) => {
            const distribution = cloudfront.Distribution.fromDistributionAttributes(this, `ImportedDistribution${index}`, {
                distributionId: config.id,
                domainName: config.domainName,
            });
            this.importedDistributions.push(distribution);
            // Output distribution information
            new cdk.CfnOutput(this, `DistributionDomainName${index}`, {
                value: config.domainName,
                description: `Domain name for ${config.comment}`,
            });
            new cdk.CfnOutput(this, `DistributionId${index}`, {
                value: config.id,
                description: `Distribution ID for ${config.comment}`,
            });
            new cdk.CfnOutput(this, `DistributionArn${index}`, {
                value: `arn:aws:cloudfront::954976309477:distribution/${config.id}`,
                description: `ARN for ${config.comment}`,
            });
        });
        // Summary output
        new cdk.CfnOutput(this, 'TotalDistributions', {
            value: discoveredDistributions.length.toString(),
            description: 'Total number of imported CloudFront distributions',
        });
    }
}
exports.CloudFrontImportStack = CloudFrontImportStack;
//# sourceMappingURL=data:application/json;base64,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