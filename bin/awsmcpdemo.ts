#!/usr/bin/env node
import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import { AwsmcpdemoStack } from '../lib/awsmcpdemo-stack';
import { CloudFrontImportStack } from '../lib/cloudfront-import-stack';

const app = new cdk.App();

// Environment configuration for your AWS account
const env = {
  account: '************',
  region: 'us-east-1',
};

// Main application stack
new AwsmcpdemoStack(app, 'AwsmcpdemoStack', {
  env,
});

// CloudFront import stack for existing distributions
new CloudFrontImportStack(app, 'CloudFrontImportStack', {
  env,
  description: 'Import existing CloudFront distributions for CDK management',
});
