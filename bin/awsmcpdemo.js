#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
require("source-map-support/register");
const cdk = __importStar(require("aws-cdk-lib"));
const awsmcpdemo_stack_1 = require("../lib/awsmcpdemo-stack");
const cloudfront_import_stack_1 = require("../lib/cloudfront-import-stack");
const app = new cdk.App();
// Environment configuration for your AWS account
const env = {
    account: '************',
    region: 'us-east-1',
};
// Main application stack
new awsmcpdemo_stack_1.AwsmcpdemoStack(app, 'AwsmcpdemoStack', {
    env,
});
// CloudFront import stack for existing distributions
new cloudfront_import_stack_1.CloudFrontImportStack(app, 'CloudFrontImportStack', {
    env,
    description: 'Import existing CloudFront distributions for CDK management',
});
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYXdzbWNwZGVtby5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbImF3c21jcGRlbW8udHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDQSx1Q0FBcUM7QUFDckMsaURBQW1DO0FBQ25DLDhEQUEwRDtBQUMxRCw0RUFBdUU7QUFFdkUsTUFBTSxHQUFHLEdBQUcsSUFBSSxHQUFHLENBQUMsR0FBRyxFQUFFLENBQUM7QUFFMUIsaURBQWlEO0FBQ2pELE1BQU0sR0FBRyxHQUFHO0lBQ1YsT0FBTyxFQUFFLGNBQWM7SUFDdkIsTUFBTSxFQUFFLFdBQVc7Q0FDcEIsQ0FBQztBQUVGLHlCQUF5QjtBQUN6QixJQUFJLGtDQUFlLENBQUMsR0FBRyxFQUFFLGlCQUFpQixFQUFFO0lBQzFDLEdBQUc7Q0FDSixDQUFDLENBQUM7QUFFSCxxREFBcUQ7QUFDckQsSUFBSSwrQ0FBcUIsQ0FBQyxHQUFHLEVBQUUsdUJBQXVCLEVBQUU7SUFDdEQsR0FBRztJQUNILFdBQVcsRUFBRSw2REFBNkQ7Q0FDM0UsQ0FBQyxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiIyEvdXNyL2Jpbi9lbnYgbm9kZVxuaW1wb3J0ICdzb3VyY2UtbWFwLXN1cHBvcnQvcmVnaXN0ZXInO1xuaW1wb3J0ICogYXMgY2RrIGZyb20gJ2F3cy1jZGstbGliJztcbmltcG9ydCB7IEF3c21jcGRlbW9TdGFjayB9IGZyb20gJy4uL2xpYi9hd3NtY3BkZW1vLXN0YWNrJztcbmltcG9ydCB7IENsb3VkRnJvbnRJbXBvcnRTdGFjayB9IGZyb20gJy4uL2xpYi9jbG91ZGZyb250LWltcG9ydC1zdGFjayc7XG5cbmNvbnN0IGFwcCA9IG5ldyBjZGsuQXBwKCk7XG5cbi8vIEVudmlyb25tZW50IGNvbmZpZ3VyYXRpb24gZm9yIHlvdXIgQVdTIGFjY291bnRcbmNvbnN0IGVudiA9IHtcbiAgYWNjb3VudDogJzk1NDk3NjMwOTQ3NycsXG4gIHJlZ2lvbjogJ3VzLWVhc3QtMScsXG59O1xuXG4vLyBNYWluIGFwcGxpY2F0aW9uIHN0YWNrXG5uZXcgQXdzbWNwZGVtb1N0YWNrKGFwcCwgJ0F3c21jcGRlbW9TdGFjaycsIHtcbiAgZW52LFxufSk7XG5cbi8vIENsb3VkRnJvbnQgaW1wb3J0IHN0YWNrIGZvciBleGlzdGluZyBkaXN0cmlidXRpb25zXG5uZXcgQ2xvdWRGcm9udEltcG9ydFN0YWNrKGFwcCwgJ0Nsb3VkRnJvbnRJbXBvcnRTdGFjaycsIHtcbiAgZW52LFxuICBkZXNjcmlwdGlvbjogJ0ltcG9ydCBleGlzdGluZyBDbG91ZEZyb250IGRpc3RyaWJ1dGlvbnMgZm9yIENESyBtYW5hZ2VtZW50Jyxcbn0pO1xuIl19